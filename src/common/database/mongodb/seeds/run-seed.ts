#!/usr/bin/env ts-node

import mongoose from 'mongoose';
import { AppDataSource } from '../../data-source';
import { User } from '@modules/users/entities/user.entity';
import { GroupAssignment } from '@modules/group-assignments/entities/group-assignment.entity';
import { Thread, Message } from '@modules/threads/entities/thread.entity';

// Fetch actual data from PostgreSQL
async function fetchPostgreSQLData() {
  console.log('� Fetching data from PostgreSQL...');

  if (!AppDataSource.isInitialized) {
    await AppDataSource.initialize();
  }

  const userRepository = AppDataSource.getRepository(User);
  const groupAssignmentRepository =
    AppDataSource.getRepository(GroupAssignment);

  const users = await userRepository.find();
  const groupAssignments = await groupAssignmentRepository.find();

  console.log(
    `✅ Fetched ${users.length} users and ${groupAssignments.length} group assignments`,
  );

  return { users, groupAssignments };
}

async function seedThreads(users: User[], groupAssignments: GroupAssignment[]) {
  console.log('🌱 Seeding threads...');

  // Check if threads already exist
  const existingThreads = await Thread.find();
  if (existingThreads.length > 0) {
    console.log('✅ Threads already exist, skipping thread seeding');
    return existingThreads;
  }

  const instructor = users.find((u) => u.isInstructor)!;
  const students = users.filter((u) => !u.isInstructor);

  if (!instructor) {
    throw new Error('No instructor found in database');
  }

  if (students.length === 0) {
    throw new Error('No students found in database');
  }

  const threadsData = [
    // Group 1 threads - First group assignment
    {
      title: 'Project Planning & Architecture Discussion',
      description: `Let's discuss the overall architecture and plan for our ${groupAssignments[0]?.name || 'project'}.`,
      groupAssignmentId: groupAssignments[0]?.id || '',
      threadUserIds: [instructor.id, students[0]?.id, students[1]?.id].filter(
        Boolean,
      ),
      createdBy: instructor.id,
    },
    {
      title: 'Frontend Development Coordination',
      description:
        'Coordination thread for frontend development tasks and React components.',
      groupAssignmentId: groupAssignments[0]?.id || '',
      threadUserIds: [students[0]?.id, students[1]?.id, students[2]?.id].filter(
        Boolean,
      ),
      createdBy: students[0]?.id,
    },
    {
      title: 'Backend API Development',
      description:
        'Discussion about backend API endpoints, database schema, and implementation.',
      groupAssignmentId: groupAssignments[0]?.id || '',
      threadUserIds: [instructor.id, students[1]?.id, students[2]?.id].filter(
        Boolean,
      ),
      createdBy: students[1]?.id,
    },
    {
      title: 'Testing & Quality Assurance',
      description:
        'Thread for discussing testing strategies, QA processes, and bug reports.',
      groupAssignmentId: groupAssignments[0]?.id || '',
      threadUserIds: [instructor.id, students[0]?.id, students[2]?.id].filter(
        Boolean,
      ),
      createdBy: instructor.id,
    },

    // Group 2 threads - Second group assignment (if exists)
    ...(groupAssignments[1]
      ? [
          {
            title: 'Database Schema Design',
            description: `Let's design the database schema for our ${groupAssignments[1].name}.`,
            groupAssignmentId: groupAssignments[1].id,
            threadUserIds: [
              instructor.id,
              students[1]?.id,
              students[2]?.id,
            ].filter(Boolean),
            createdBy: instructor.id,
          },
          {
            title: 'Data Migration & Optimization',
            description:
              'Discussion about data migration strategies and database optimization.',
            groupAssignmentId: groupAssignments[1].id,
            threadUserIds: [
              students[1]?.id,
              students[2]?.id,
              students[0]?.id,
            ].filter(Boolean),
            createdBy: students[1]?.id,
          },
          {
            title: 'Performance Analysis',
            description:
              'Analyzing system performance and identifying bottlenecks.',
            groupAssignmentId: groupAssignments[1].id,
            threadUserIds: [
              instructor.id,
              students[0]?.id,
              students[1]?.id,
            ].filter(Boolean),
            createdBy: students[0]?.id,
          },
        ]
      : []),

    // Group 3 threads - Third group assignment (if exists)
    ...(groupAssignments[2]
      ? [
          {
            title: 'ML Model Development',
            description: `Discussion about machine learning model development for ${groupAssignments[2].name}.`,
            groupAssignmentId: groupAssignments[2].id,
            threadUserIds: [
              instructor.id,
              students[0]?.id,
              students[2]?.id,
            ].filter(Boolean),
            createdBy: instructor.id,
          },
          {
            title: 'Data Preprocessing & Feature Engineering',
            description:
              'Coordinating data preprocessing tasks and feature engineering strategies.',
            groupAssignmentId: groupAssignments[2].id,
            threadUserIds: [
              students[0]?.id,
              students[1]?.id,
              students[2]?.id,
            ].filter(Boolean),
            createdBy: students[0]?.id,
          },
          {
            title: 'Model Evaluation & Deployment',
            description:
              'Discussion about model evaluation metrics and deployment strategies.',
            groupAssignmentId: groupAssignments[2].id,
            threadUserIds: [
              instructor.id,
              students[1]?.id,
              students[2]?.id,
            ].filter(Boolean),
            createdBy: students[1]?.id,
          },
        ]
      : []),

    // Additional general threads
    {
      title: 'General Q&A and Help',
      description: 'General questions and answers thread for all students.',
      groupAssignmentId: groupAssignments[0]?.id || '',
      threadUserIds: [instructor.id, ...students.map((s) => s.id)].filter(
        Boolean,
      ),
      createdBy: instructor.id,
    },
    {
      title: 'Resource Sharing',
      description: 'Share useful resources, links, and documentation.',
      groupAssignmentId: groupAssignments[0]?.id || '',
      threadUserIds: [instructor.id, ...students.map((s) => s.id)].filter(
        Boolean,
      ),
      createdBy: students[0]?.id,
    },
  ];

  const savedThreads: any[] = [];
  for (const threadData of threadsData) {
    if (threadData.groupAssignmentId && threadData.createdBy) {
      const thread = new Thread(threadData);
      const savedThread = await thread.save();
      savedThreads.push(savedThread);
    }
  }

  console.log(`✅ Created ${savedThreads.length} threads`);
  return savedThreads;
}

async function seedMessages(threads: any[], users: User[]) {
  console.log('🌱 Seeding messages...');

  // Check if messages already exist
  const existingMessages = await Message.find();
  if (existingMessages.length > 0) {
    console.log('✅ Messages already exist, skipping message seeding');
    return existingMessages;
  }

  const messagesData: any[] = [];

  // Generate messages for each thread
  for (let i = 0; i < threads.length; i++) {
    const thread = threads[i];
    const threadUsers = users.filter((u) =>
      thread.threadUserIds.includes(u.id),
    );

    if (threadUsers.length === 0) continue;

    // Generate 5-10 messages per thread
    const messageCount = Math.floor(Math.random() * 6) + 5; // 5-10 messages
    const baseDate = new Date('2024-01-15T09:00:00Z');

    for (let j = 0; j < messageCount; j++) {
      const sender = threadUsers[j % threadUsers.length];
      const messageDate = new Date(
        baseDate.getTime() + i * 24 * 60 * 60 * 1000 + j * 2 * 60 * 60 * 1000,
      ); // Spread messages over time

      let content = '';

      // Generate realistic content based on thread title and message position
      if (j === 0) {
        // First message - usually from thread creator
        if (thread.title.includes('Planning')) {
          content = `Welcome everyone! Let's start planning our approach for this ${thread.title.toLowerCase()}. What are your initial thoughts?`;
        } else if (thread.title.includes('Development')) {
          content = `Hi team! I've created this thread to coordinate our development efforts. Let's discuss our strategy.`;
        } else if (thread.title.includes('Database')) {
          content = `Let's begin by discussing the database requirements and schema design for our project.`;
        } else if (thread.title.includes('Testing')) {
          content = `Time to focus on testing! What testing frameworks and strategies should we use?`;
        } else if (thread.title.includes('ML Model')) {
          content = `Let's discuss our machine learning approach and model selection criteria.`;
        } else {
          content = `Welcome to the ${thread.title} discussion! Looking forward to collaborating with everyone.`;
        }
      } else {
        // Subsequent messages - varied responses
        const responses = [
          `Great point! I think we should also consider the scalability aspects.`,
          `I agree with the previous suggestions. Here's what I think we should add...`,
          `That's a solid approach. Should we also look into performance optimization?`,
          `Thanks for sharing that resource! It's very helpful for our implementation.`,
          `I've been working on this part and encountered an interesting challenge...`,
          `Good progress everyone! Here's an update on my assigned tasks.`,
          `I found a useful library that might help us with this functionality.`,
          `Let me share some documentation that could be relevant to our discussion.`,
          `Has anyone tried implementing this feature yet? I'd love to hear about your experience.`,
          `I think we need to revisit our initial assumptions about this requirement.`,
          `The testing results look promising. Here are the metrics I gathered...`,
          `I've pushed some code changes. Could someone review them when you have time?`,
          `We might want to consider an alternative approach for better maintainability.`,
          `Great collaboration so far! I'm impressed with our team's progress.`,
          `I'll take care of the documentation for this feature by tomorrow.`,
          `Should we schedule a quick sync meeting to discuss the next steps?`,
          `I've updated the project board with our latest progress and blockers.`,
          `The integration is working well. Here are some screenshots of the results.`,
          `I think we're ready to move to the next phase of development.`,
          `Thanks everyone for the productive discussion! Let's implement these ideas.`,
        ];

        content = responses[Math.floor(Math.random() * responses.length)];

        // Add some context-specific content
        if (thread.title.includes('Frontend') && Math.random() > 0.7) {
          content += ` I'm thinking we should use React hooks for state management.`;
        } else if (thread.title.includes('Backend') && Math.random() > 0.7) {
          content += ` The API endpoints are looking good so far.`;
        } else if (thread.title.includes('Database') && Math.random() > 0.7) {
          content += ` We should ensure proper indexing for performance.`;
        } else if (thread.title.includes('Testing') && Math.random() > 0.7) {
          content += ` Unit tests are passing, now working on integration tests.`;
        }
      }

      messagesData.push({
        threadId: thread._id.toString(),
        senderId: sender.id,
        content: content,
        timestamp: messageDate,
      });
    }
  }

  // Save all messages
  const savedMessages: any[] = [];
  for (const messageData of messagesData) {
    const message = new Message(messageData);
    const savedMessage = await message.save();
    savedMessages.push(savedMessage);
  }

  console.log(`✅ Created ${savedMessages.length} messages`);
  return savedMessages;
}

async function runMongoSeeds(standalone: boolean = true) {
  if (standalone) {
    console.log('🚀 Starting MongoDB seeding...');
  }

  try {
    // Connect to MongoDB
    const mongoUri =
      process.env.MONGODB_URI ||
      '*************************************************************************';
    if (
      mongoose.connection.readyState !== mongoose.ConnectionStates.connected
    ) {
      await mongoose.connect(mongoUri);
    }
    console.log('✅ MongoDB connection established');

    // Fetch data from PostgreSQL
    const { users, groupAssignments } = await fetchPostgreSQLData();

    // Seed data in the correct order
    const threads = await seedThreads(users, groupAssignments);
    const messages = await seedMessages(threads, users);

    console.log('\n📊 MongoDB Summary:');
    console.log(`   💬 Threads: ${threads.length}`);
    console.log(`   📝 Messages: ${messages.length}`);

    // Close MongoDB connection
    await mongoose.connection.close();
    console.log('✅ MongoDB connection closed');

    // Only close PostgreSQL connection and exit if running standalone
    if (standalone) {
      if (AppDataSource.isInitialized) {
        await AppDataSource.destroy();
        console.log('✅ PostgreSQL connection closed');
      }

      console.log('\n🎉 MongoDB seeding completed successfully!');
      process.exit(0);
    }
  } catch (err) {
    console.error('❌ Error while running MongoDB seeders:', err);
    if (standalone) {
      process.exit(1);
    } else {
      throw err; // Re-throw error for parent process to handle
    }
  }
}

// Run if this file is executed directly
if (require.main === module) {
  void runMongoSeeds(true); // Run in standalone mode
}

export { runMongoSeeds };
